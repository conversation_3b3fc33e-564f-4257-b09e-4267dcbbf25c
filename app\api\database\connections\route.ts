import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { requireAuth } from '@/lib/auth';
import { executeInTransaction, createAuditLogInTransaction } from '@/lib/transaction-utils';
import bcrypt from 'bcryptjs';

export async function GET(request: NextRequest) {
  try {
    // التحقق من التفويض
    const authResult = await requireAuth(request, 'admin');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    const connections = await prisma.databaseConnection.findMany({
      select: {
        id: true,
        name: true,
        host: true,
        port: true,
        database: true,
        username: true,
        isActive: true,
        isDefault: true,
        createdAt: true,
        updatedAt: true,
        // لا نعرض كلمة المرور
      },
      orderBy: { createdAt: 'desc' }
    });

    return NextResponse.json({
      success: true,
      connections
    });
  } catch (error) {
    console.error('Failed to fetch connections:', error);
    return NextResponse.json(
      { error: 'Failed to fetch database connections' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    // التحقق من التفويض
    const authResult = await requireAuth(request, 'admin');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    const data = await request.json();

    // التحقق من البيانات المطلوبة
    if (!data.name || !data.host || !data.database || !data.username || !data.password) {
      return NextResponse.json(
        { error: 'All connection fields are required' },
        { status: 400 }
      );
    }

    // تنفيذ العملية داخل معاملة
    const result = await executeInTransaction(async (tx) => {
      // تشفير كلمة المرور
      const hashedPassword = await bcrypt.hash(data.password, 10);

      // إذا كان هذا الاتصال الافتراضي، قم بإلغاء الافتراضي للآخرين
      if (data.isDefault) {
        await tx.databaseConnection.updateMany({
          where: { isDefault: true },
          data: { isDefault: false }
        });
      }

      const connection = await tx.databaseConnection.create({
        data: {
          name: data.name,
          host: data.host,
          port: data.port || 5432,
          database: data.database,
          username: data.username,
          password: hashedPassword,
          isActive: data.isActive || false,
          isDefault: data.isDefault || false,
        }
      });

      // إنشاء audit log
      await createAuditLogInTransaction(tx, {
        userId: authResult.user!.id,
        username: authResult.user!.username,
        operation: 'CREATE',
        details: `Created database connection: ${connection.name}`,
        tableName: 'databaseConnection',
        recordId: connection.id.toString()
      });

      return {
        ...connection,
        password: undefined // لا نعيد كلمة المرور
      };
    });

    return NextResponse.json(result, { status: 201 });
  } catch (error) {
    console.error('Failed to create connection:', error);
    return NextResponse.json(
      { error: 'Failed to create database connection' },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest) {
  try {
    // التحقق من التفويض
    const authResult = await requireAuth(request, 'admin');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    const data = await request.json();

    if (!data.id) {
      return NextResponse.json(
        { error: 'Connection ID is required' },
        { status: 400 }
      );
    }

    // تنفيذ العملية داخل معاملة
    const result = await executeInTransaction(async (tx) => {
      // التحقق من وجود الاتصال
      const existingConnection = await tx.databaseConnection.findUnique({
        where: { id: data.id }
      });

      if (!existingConnection) {
        throw new Error('Database connection not found');
      }

      // إذا كان هذا الاتصال الافتراضي، قم بإلغاء الافتراضي للآخرين
      if (data.isDefault && !existingConnection.isDefault) {
        await tx.databaseConnection.updateMany({
          where: { isDefault: true },
          data: { isDefault: false }
        });
      }

      // تحديث البيانات
      const updateData: any = {
        name: data.name || existingConnection.name,
        host: data.host || existingConnection.host,
        port: data.port || existingConnection.port,
        database: data.database || existingConnection.database,
        username: data.username || existingConnection.username,
        isActive: data.isActive !== undefined ? data.isActive : existingConnection.isActive,
        isDefault: data.isDefault !== undefined ? data.isDefault : existingConnection.isDefault,
      };

      // تشفير كلمة المرور الجديدة إذا تم تمريرها
      if (data.password) {
        updateData.password = await bcrypt.hash(data.password, 10);
      }

      const connection = await tx.databaseConnection.update({
        where: { id: data.id },
        data: updateData
      });

      // إنشاء audit log
      await createAuditLogInTransaction(tx, {
        userId: authResult.user!.id,
        username: authResult.user!.username,
        operation: 'UPDATE',
        details: `Updated database connection: ${connection.name}`,
        tableName: 'databaseConnection',
        recordId: connection.id.toString()
      });

      return {
        ...connection,
        password: undefined
      };
    });

    return NextResponse.json(result);
  } catch (error) {
    console.error('Failed to update connection:', error);

    if (error instanceof Error && error.message === 'Database connection not found') {
      return NextResponse.json({ error: 'Database connection not found' }, { status: 404 });
    }

    return NextResponse.json(
      { error: 'Failed to update database connection' },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    // التحقق من التفويض
    const authResult = await requireAuth(request, 'admin');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    const { id } = await request.json();

    if (!id) {
      return NextResponse.json(
        { error: 'Connection ID is required' },
        { status: 400 }
      );
    }

    // تنفيذ العملية داخل معاملة
    const result = await executeInTransaction(async (tx) => {
      // التحقق من وجود الاتصال
      const existingConnection = await tx.databaseConnection.findUnique({
        where: { id }
      });

      if (!existingConnection) {
        throw new Error('Database connection not found');
      }

      // حذف الاتصال (سيحذف النسخ الاحتياطية المرتبطة تلقائياً بسبب onDelete: Cascade)
      await tx.databaseConnection.delete({
        where: { id }
      });

      // إنشاء audit log
      await createAuditLogInTransaction(tx, {
        userId: authResult.user!.id,
        username: authResult.user!.username,
        operation: 'DELETE',
        details: `Deleted database connection: ${existingConnection.name}`,
        tableName: 'databaseConnection',
        recordId: id.toString()
      });

      return { message: 'Database connection deleted successfully' };
    });

    return NextResponse.json(result);
  } catch (error) {
    console.error('Failed to delete connection:', error);

    if (error instanceof Error && error.message === 'Database connection not found') {
      return NextResponse.json({ error: 'Database connection not found' }, { status: 404 });
    }

    return NextResponse.json(
      { error: 'Failed to delete database connection' },
      { status: 500 }
    );
  }
}
