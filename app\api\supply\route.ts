import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { requireAuth } from '@/lib/auth';
import { executeInTransaction, createAuditLogInTransaction, generateUniqueId } from '@/lib/transaction-utils';

export async function GET(request: NextRequest) {
  try {
    // التحقق من التفويض
    const authResult = await requireAuth(request, 'user');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    const supplyOrders = await prisma.supplyOrder.findMany({
      orderBy: { id: 'desc' }
    });
    return NextResponse.json(supplyOrders);
  } catch (error) {
    console.error('Failed to fetch supply orders:', error);
    return NextResponse.json({ error: 'Failed to fetch supply orders' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    // التحقق من التفويض
    const authResult = await requireAuth(request, 'user');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    const newOrder = await request.json();

    // Basic validation
    if (!newOrder.supplierId || !newOrder.items || !Array.isArray(newOrder.items) || newOrder.items.length === 0) {
      return NextResponse.json(
        { error: 'Supplier ID and items are required' },
        { status: 400 }
      );
    }

    // تنفيذ العملية داخل معاملة
    const result = await executeInTransaction(async (tx) => {
      // Generate unique supply order ID
      const supplyOrderId = await generateUniqueId(tx, 'supplyOrder', 'SUP-');

      // Create the order in the database
      const order = await tx.supplyOrder.create({
        data: {
          supplyOrderId,
          supplierId: newOrder.supplierId,
          invoiceNumber: newOrder.invoiceNumber || null,
          supplyDate: newOrder.supplyDate,
          warehouseId: newOrder.warehouseId || null,
          employeeName: newOrder.employeeName || authResult.user!.username,
          items: typeof newOrder.items === 'object' ? JSON.stringify(newOrder.items) : newOrder.items,
          notes: newOrder.notes || '',
          invoiceFileName: newOrder.invoiceFileName || null,
          referenceNumber: newOrder.referenceNumber || null,
          status: newOrder.status || 'completed'
        }
      });

      // Add devices to database if needed
      if (newOrder.items && Array.isArray(newOrder.items)) {
        for (const item of newOrder.items) {
          if (item.imei) {
            try {
              // Check if device already exists
              const existingDevice = await tx.device.findUnique({
                where: { id: item.imei }
              });

              if (!existingDevice) {
                await tx.device.create({
                  data: {
                    id: item.imei,
                    model: `${item.manufacturer} ${item.model}`,
                    status: 'متاح للبيع',
                    storage: 'N/A',
                    price: 0,
                    condition: item.condition || 'جديد',
                    warehouseId: newOrder.warehouseId,
                    supplierId: newOrder.supplierId
                  }
                });
              }
            } catch (deviceError) {
              console.error('Failed to create device:', deviceError);
            }
          }
        }
      }

      // إنشاء audit log
      await createAuditLogInTransaction(tx, {
        userId: authResult.user!.id,
        username: authResult.user!.username,
        operation: 'CREATE',
        details: `Created supply order: ${order.supplyOrderId}`,
        tableName: 'supplyOrder',
        recordId: order.id.toString()
      });

      return order;
    });

    return NextResponse.json(result, { status: 201 });
  } catch (error) {
    console.error('Failed to create supply order:', error);
    return NextResponse.json({ error: 'Failed to create supply order' }, { status: 500 });
  }
}

export async function PUT(request: NextRequest) {
  try {
    // التحقق من التفويض
    const authResult = await requireAuth(request, 'user');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    const updatedOrder = await request.json();

    if (!updatedOrder.id) {
      return NextResponse.json({ error: 'Supply order ID is required' }, { status: 400 });
    }

    // تنفيذ العملية داخل معاملة
    const result = await executeInTransaction(async (tx) => {
      // Check if order exists
      const existingOrder = await tx.supplyOrder.findUnique({
        where: { id: updatedOrder.id }
      });

      if (!existingOrder) {
        throw new Error('Supply order not found');
      }

      // Update the order
      const order = await tx.supplyOrder.update({
        where: { id: updatedOrder.id },
        data: {
          supplierId: updatedOrder.supplierId,
          invoiceNumber: updatedOrder.invoiceNumber,
          supplyDate: updatedOrder.supplyDate,
          warehouseId: updatedOrder.warehouseId,
          employeeName: updatedOrder.employeeName,
          items: typeof updatedOrder.items === 'object' ? JSON.stringify(updatedOrder.items) : updatedOrder.items,
          notes: updatedOrder.notes || '',
          invoiceFileName: updatedOrder.invoiceFileName,
          referenceNumber: updatedOrder.referenceNumber,
          status: updatedOrder.status || 'completed'
        }
      });

      // إنشاء audit log
      await createAuditLogInTransaction(tx, {
        userId: authResult.user!.id,
        username: authResult.user!.username,
        operation: 'UPDATE',
        details: `Updated supply order: ${order.supplyOrderId}`,
        tableName: 'supplyOrder',
        recordId: order.id.toString()
      });

      return order;
    });

    return NextResponse.json(result);
  } catch (error) {
    console.error('Failed to update supply order:', error);

    if (error instanceof Error && error.message === 'Supply order not found') {
      return NextResponse.json({ error: 'Supply order not found' }, { status: 404 });
    }

    return NextResponse.json({ error: 'Failed to update supply order' }, { status: 500 });
  }
}

export async function DELETE(request: NextRequest) {
  try {
    // التحقق من التفويض
    const authResult = await requireAuth(request, 'manager');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    const { id } = await request.json();

    if (!id) {
      return NextResponse.json({ error: 'Supply order ID is required' }, { status: 400 });
    }

    // تنفيذ العملية داخل معاملة
    const result = await executeInTransaction(async (tx) => {
      // Check if order exists
      const existingOrder = await tx.supplyOrder.findUnique({
        where: { id }
      });

      if (!existingOrder) {
        throw new Error('Supply order not found');
      }

      // Delete the order
      await tx.supplyOrder.delete({
        where: { id }
      });

      // إنشاء audit log
      await createAuditLogInTransaction(tx, {
        userId: authResult.user!.id,
        username: authResult.user!.username,
        operation: 'DELETE',
        details: `Deleted supply order: ${existingOrder.supplyOrderId}`,
        tableName: 'supplyOrder',
        recordId: id.toString()
      });

      return { message: 'Supply order deleted successfully' };
    });

    return NextResponse.json(result);
  } catch (error) {
    console.error('Failed to delete supply order:', error);

    if (error instanceof Error && error.message === 'Supply order not found') {
      return NextResponse.json({ error: 'Supply order not found' }, { status: 404 });
    }

    return NextResponse.json({ error: 'Failed to delete supply order' }, { status: 500 });
  }
}
