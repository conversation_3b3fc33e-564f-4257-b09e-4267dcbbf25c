import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { requireAuth } from '@/lib/auth';
import { executeInTransaction, createAuditLogInTransaction, checkRelationsBeforeDelete, generateUniqueId } from '@/lib/transaction-utils';

export async function GET(request: NextRequest) {
  try {
    // التحقق من التفويض
    const authResult = await requireAuth(request, 'user');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    const deliveryOrders = await prisma.deliveryOrder.findMany({
      orderBy: { id: 'desc' }
    });

    // تحويل حقول JSON من strings إلى objects
    const processedOrders = deliveryOrders.map((order: any) => ({
      ...order,
      items: order.items ?
        (typeof order.items === 'string' ?
          JSON.parse(order.items) : order.items) : [],
    }));

    return NextResponse.json(processedOrders);
  } catch (error) {
    console.error('Failed to fetch delivery orders:', error);
    return NextResponse.json({ error: 'Failed to fetch delivery orders' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    // التحقق من التفويض
    const authResult = await requireAuth(request, 'user');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    const newOrder = await request.json();

    // Basic validation
    if (!newOrder.date || !newOrder.warehouseId) {
      return NextResponse.json(
        { error: 'Date and warehouse ID are required' },
        { status: 400 },
      );
    }

    // تنفيذ العملية داخل معاملة
    const result = await executeInTransaction(async (tx) => {
      // Generate unique delivery order number
      const deliveryOrderNumber = await generateUniqueId(tx, 'deliveryOrder', 'DEL-');

      // Create the delivery order in the database
      const order = await tx.deliveryOrder.create({
        data: {
          deliveryOrderNumber,
          referenceNumber: newOrder.referenceNumber || null,
          date: newOrder.date,
          warehouseId: newOrder.warehouseId,
          warehouseName: newOrder.warehouseName,
          employeeName: newOrder.employeeName,
          items: newOrder.items ? JSON.stringify(newOrder.items) : JSON.stringify([]),
          notes: newOrder.notes || null,
          status: newOrder.status || 'completed',
          attachmentName: newOrder.attachmentName || null,
        }
      });

      // Update device statuses based on maintenance result
      if (newOrder.items && Array.isArray(newOrder.items)) {
        for (const item of newOrder.items) {
          let finalStatus;
          switch (item.result) {
            case 'Repaired':
              finalStatus = 'متاح للبيع';
              break;
            case 'Unrepairable-Defective':
              finalStatus = 'معيب';
              break;
            case 'Unrepairable-Damaged':
              finalStatus = 'تالف';
              break;
            default:
              finalStatus = 'متاح للبيع';
          }

          await tx.device.update({
            where: { id: item.deviceId },
            data: {
              status: finalStatus,
              warehouseId: newOrder.warehouseId
            }
          });

          // Create maintenance log entry with proper date handling
          const repairDate = new Date(newOrder.date);
          if (isNaN(repairDate.getTime())) {
            throw new Error('Invalid date format');
          }

          await tx.maintenanceLog.create({
            data: {
              deviceId: item.deviceId,
              model: item.model,
              repairDate: repairDate.toISOString(),
              notes: item.notes || item.fault || 'تم إنهاء الصيانة',
              result: item.result,
              status: 'pending'
            }
          });
        }
      }

      // إنشاء audit log
      await createAuditLogInTransaction(tx, {
        userId: authResult.user!.id,
        username: authResult.user!.username,
        operation: 'CREATE',
        details: `Created delivery order: ${deliveryOrderNumber}`,
        tableName: 'deliveryOrder',
        recordId: order.id.toString()
      });

      return order;
    });

    // معالجة حقول JSON قبل الإرسال
    const processedOrder = {
      ...result,
      items: result.items ?
        (typeof result.items === 'string' ?
          JSON.parse(result.items) : result.items) : [],
    };

    return NextResponse.json(processedOrder, { status: 201 });
  } catch (error) {
    console.error('Failed to create delivery order:', error);

    if (error instanceof Error && error.message === 'Invalid date format') {
      return NextResponse.json({ error: 'Invalid date format' }, { status: 400 });
    }

    return NextResponse.json({ error: 'Failed to create delivery order' }, { status: 500 });
  }
}

export async function PUT(request: NextRequest) {
  try {
    // التحقق من التفويض
    const authResult = await requireAuth(request, 'user');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    const updatedOrder = await request.json();

    // تنفيذ العملية داخل معاملة
    const result = await executeInTransaction(async (tx) => {
      // Check if delivery order exists
      const existingOrder = await tx.deliveryOrder.findUnique({
        where: { id: updatedOrder.id }
      });

      if (!existingOrder) {
        throw new Error('Delivery order not found');
      }

      // Parse items to handle device status updates
      const oldItems = existingOrder.items as any;
      let oldItemsArray = [];
      try {
        oldItemsArray = typeof oldItems === 'string' ? JSON.parse(oldItems) : oldItems;
      } catch (error) {
        console.warn('Failed to parse old items:', error);
      }

      // Update the delivery order
      const order = await tx.deliveryOrder.update({
        where: { id: updatedOrder.id },
        data: {
          deliveryOrderNumber: updatedOrder.deliveryOrderNumber,
          referenceNumber: updatedOrder.referenceNumber,
          date: updatedOrder.date,
          warehouseId: updatedOrder.warehouseId,
          warehouseName: updatedOrder.warehouseName,
          employeeName: updatedOrder.employeeName,
          items: updatedOrder.items ? JSON.stringify(updatedOrder.items) : JSON.stringify([]),
          notes: updatedOrder.notes,
          status: updatedOrder.status,
          attachmentName: updatedOrder.attachmentName,
        }
      });

      // Update device statuses based on maintenance result
      if (updatedOrder.items && Array.isArray(updatedOrder.items)) {
        for (const item of updatedOrder.items) {
          let finalStatus;
          switch (item.result) {
            case 'Repaired':
              finalStatus = 'متاح للبيع';
              break;
            case 'Unrepairable-Defective':
              finalStatus = 'معيب';
              break;
            case 'Unrepairable-Damaged':
              finalStatus = 'تالف';
              break;
            default:
              finalStatus = 'متاح للبيع';
          }

          await tx.device.update({
            where: { id: item.deviceId },
            data: {
              status: finalStatus,
              warehouseId: updatedOrder.warehouseId
            }
          });
        }
      }

      // إنشاء audit log
      await createAuditLogInTransaction(tx, {
        userId: authResult.user!.id,
        username: authResult.user!.username,
        operation: 'UPDATE',
        details: `Updated delivery order: ${order.deliveryOrderNumber}`,
        tableName: 'deliveryOrder',
        recordId: order.id.toString()
      });

      return order;
    });

    // معالجة حقول JSON قبل الإرسال
    const processedOrder = {
      ...result,
      items: result.items ?
        (typeof result.items === 'string' ?
          JSON.parse(result.items) : result.items) : [],
    };

    return NextResponse.json(processedOrder);
  } catch (error) {
    console.error('Failed to update delivery order:', error);

    if (error instanceof Error && error.message === 'Delivery order not found') {
      return NextResponse.json({ error: 'Delivery order not found' }, { status: 404 });
    }

    return NextResponse.json({ error: 'Failed to update delivery order' }, { status: 500 });
  }
}

export async function DELETE(request: NextRequest) {
  try {
    // التحقق من التفويض - يتطلب صلاحيات إدارية للحذف
    const authResult = await requireAuth(request, 'manager');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    const { id } = await request.json();

    // تنفيذ العملية داخل معاملة
    const result = await executeInTransaction(async (tx) => {
      // Check if delivery order exists
      const existingOrder = await tx.deliveryOrder.findUnique({
        where: { id: parseInt(id) }
      });

      if (!existingOrder) {
        throw new Error('Delivery order not found');
      }

      // Parse items to update device statuses back
      let items = [];
      try {
        items = typeof existingOrder.items === 'string' ?
          JSON.parse(existingOrder.items) : existingOrder.items;
      } catch (error) {
        console.warn('Failed to parse items for device status update:', error);
      }

      // Update device statuses back to maintenance
      if (Array.isArray(items)) {
        for (const item of items) {
          await tx.device.update({
            where: { id: item.deviceId },
            data: { status: 'قيد الإصلاح' }
          });
        }
      }

      // Delete the delivery order
      await tx.deliveryOrder.delete({
        where: { id: parseInt(id) }
      });

      // إنشاء audit log
      await createAuditLogInTransaction(tx, {
        userId: authResult.user!.id,
        username: authResult.user!.username,
        operation: 'DELETE',
        details: `Deleted delivery order: ${existingOrder.deliveryOrderNumber}`,
        tableName: 'deliveryOrder',
        recordId: id
      });

      return { message: 'Delivery order deleted successfully' };
    });

    return NextResponse.json(result);
  } catch (error) {
    console.error('Failed to delete delivery order:', error);

    if (error instanceof Error && error.message === 'Delivery order not found') {
      return NextResponse.json({ error: 'Delivery order not found' }, { status: 404 });
    }

    return NextResponse.json({ error: 'Failed to delete delivery order' }, { status: 500 });
  }
}
