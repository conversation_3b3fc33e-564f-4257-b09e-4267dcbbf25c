import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { requireAuth } from '@/lib/auth';
import { executeInTransaction, createAuditLogInTransaction } from '@/lib/transaction-utils';
import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

export async function POST(request: NextRequest) {
  try {
    // التحقق من التفويض - يتطلب صلاحيات إدارية
    const authResult = await requireAuth(request, 'admin');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    const { connectionId, name, owner, template, encoding } = await request.json();

    // التحقق من صحة البيانات
    if (!connectionId || !name) {
      return NextResponse.json(
        { error: 'Connection ID and database name are required' },
        { status: 400 }
      );
    }

    // التحقق من صحة اسم قاعدة البيانات (منع SQL injection)
    if (!/^[a-zA-Z][a-zA-Z0-9_]*$/.test(name)) {
      return NextResponse.json(
        { error: 'Invalid database name. Use only letters, numbers, and underscores.' },
        { status: 400 }
      );
    }

    // تنفيذ العملية داخل معاملة
    const result = await executeInTransaction(async (tx) => {
      // الحصول على معلومات الاتصال
      const connection = await tx.databaseConnection.findUnique({
        where: { id: connectionId }
      });

      if (!connection) {
        throw new Error('Connection not found');
      }

      // فك تشفير كلمة المرور (يحتاج تحسين في التطبيق الحقيقي)
      const password = connection.password;

      // التحقق من وجود قاعدة البيانات مسبقاً
      const checkCommand = `psql -h ${connection.host} -p ${connection.port} -U ${connection.username} -lqt | cut -d \\| -f 1 | grep -qw ${name}`;
      const env = { ...process.env, PGPASSWORD: password };

      try {
        await execAsync(checkCommand, { env });
        throw new Error('Database already exists');
      } catch (checkError) {
        // إذا لم توجد قاعدة البيانات، فهذا جيد
        if (checkError instanceof Error && !checkError.message.includes('already exists')) {
          // المتابعة لإنشاء قاعدة البيانات
        } else {
          throw checkError;
        }
      }

      // تنفيذ أمر إنشاء قاعدة البيانات
      const createCommand = `createdb -h ${connection.host} -p ${connection.port} -U ${connection.username} -O ${owner || connection.username} -T ${template || 'template0'} -E ${encoding || 'UTF8'} ${name}`;

      await execAsync(createCommand, { env });

      // حفظ معلومات قاعدة البيانات الجديدة
      const newDatabase = await tx.database.create({
        data: {
          name,
          connectionId,
          owner: owner || connection.username,
          template: template || 'template0',
          encoding: encoding || 'UTF8',
          createdBy: authResult.user!.id,
          createdAt: new Date()
        }
      });

      // إنشاء audit log
      await createAuditLogInTransaction(tx, {
        userId: authResult.user!.id,
        username: authResult.user!.username,
        operation: 'CREATE',
        details: `Created database: ${name} on connection: ${connection.name}`,
        tableName: 'database',
        recordId: newDatabase.id.toString()
      });

      return {
        message: 'Database created successfully',
        database: {
          id: newDatabase.id,
          name: newDatabase.name,
          owner: newDatabase.owner,
          template: newDatabase.template,
          encoding: newDatabase.encoding,
          createdAt: newDatabase.createdAt
        }
      };
    });

    return NextResponse.json(result, { status: 201 });
  } catch (error) {
    console.error('Create database error:', error);

    // التحقق من نوع الخطأ لإعطاء رسالة واضحة
    if (error instanceof Error) {
      if (error.message.includes('already exists')) {
        return NextResponse.json(
          { error: 'Database already exists' },
          { status: 409 }
        );
      }
      if (error.message.includes('permission denied')) {
        return NextResponse.json(
          { error: 'Permission denied to create database' },
          { status: 403 }
        );
      }
      if (error.message.includes('Connection not found')) {
        return NextResponse.json(
          { error: 'Database connection not found' },
          { status: 404 }
        );
      }
    }

    return NextResponse.json(
      { error: 'Failed to create database' },
      { status: 500 }
    );
  }
}

// الحصول على قائمة قواعد البيانات
export async function GET(request: NextRequest) {
  try {
    // التحقق من التفويض
    const authResult = await requireAuth(request, 'admin');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const connectionId = searchParams.get('connectionId');

    if (!connectionId) {
      return NextResponse.json(
        { error: 'Connection ID is required' },
        { status: 400 }
      );
    }

    // الحصول على قواعد البيانات من قاعدة البيانات المحلية
    const databases = await prisma.database.findMany({
      where: { connectionId },
      include: {
        connection: {
          select: {
            name: true,
            host: true,
            port: true
          }
        }
      },
      orderBy: { createdAt: 'desc' }
    });

    return NextResponse.json({
      success: true,
      databases
    });

  } catch (error) {
    console.error('Get databases error:', error);
    return NextResponse.json(
      { error: 'Failed to get databases' },
      { status: 500 }
    );
  }
}
