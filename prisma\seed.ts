import { PrismaClient } from '../generated/prisma';

const prisma = new PrismaClient();

async function main() {
  if (await prisma.user.count() === 0) {
    await prisma.user.create({
      data: {
        name: 'Admin',
        email: '<EMAIL>',
      },
    });
  }
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });